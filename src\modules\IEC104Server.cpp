#include "IEC104Server.h"

// Constructor
IEC104Server::IEC104Server() : slave(nullptr), isRunning(false), connectedClients(0) {
}

// Destructor
IEC104Server::~IEC104Server() {
    if (slave) {
        stop();
        CS104_Slave_destroy(slave);
    }
}

// Initialize the IEC104 server
bool IEC104Server::initialize(const char* localIP, int maxConnections, int messageQueueSize) {
    // Create a new slave/server instance
    slave = CS104_Slave_create(maxConnections, messageQueueSize);
    if (!slave) {
        Serial.println("Failed to create IEC104 slave");
        return false;
    }
    
    // Set local IP address
    CS104_Slave_setLocalAddress(slave, localIP);
    
    // Set server mode to single redundancy group
    CS104_Slave_setServerMode(slave, CS104_MODE_SINGLE_REDUNDANCY_GROUP);
    
    // Get parameters
    alParams = CS104_Slave_getAppLayerParameters(slave);
    apciParams = CS104_Slave_getConnectionParameters(slave);
    
    // Set callback handlers
    CS104_Slave_setClockSyncHandler(slave, clockSyncHandler, this);
    CS104_Slave_setInterrogationHandler(slave, interrogationHandler, this);
    CS104_Slave_setASDUHandler(slave, asduHandler, this);
    CS104_Slave_setConnectionRequestHandler(slave, connectionRequestHandler, this);
    CS104_Slave_setConnectionEventHandler(slave, connectionEventHandler, this);
    
    printAPCIParameters();
    
    return true;
}

// Set server mode
void IEC104Server::setServerMode(CS104_ServerMode mode) {
    if (slave) {
        CS104_Slave_setServerMode(slave, mode);
    }
}

// Enable or disable raw message logging
void IEC104Server::enableRawMessageLogging(bool enable) {
    if (slave) {
        if (enable) {
            CS104_Slave_setRawMessageHandler(slave, rawMessageHandler, this);
        } else {
            CS104_Slave_setRawMessageHandler(slave, nullptr, nullptr);
        }
    }
}

// Start the server
bool IEC104Server::start() {
    if (!slave) {
        Serial.println("IEC104 server not initialized");
        return false;
    }

    CS104_Slave_start(slave);

    if (CS104_Slave_isRunning(slave)) {
        isRunning = true;
        Serial.println("IEC104 server started successfully");
        return true;
    } else {
        isRunning = false;
        Serial.println("Failed to start IEC104 server");
        return false;
    }
}

// Stop the server
void IEC104Server::stop() {
    if (slave) {
        Serial.println("Stopping IEC104 server");
        CS104_Slave_stop(slave);
        isRunning = false;
        connectedClients = 0;
    }
}


// Send measured value
void IEC104Server::sendMeasuredValue(int ioa, int16_t value, QualityDescriptor quality) {
    if (!slave) return;
    
    CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_SPONTANEOUS, 0, 1, false, false);
    
    InformationObject io = (InformationObject) MeasuredValueScaled_create(NULL, ioa, value, quality);
    CS101_ASDU_addInformationObject(newAsdu, io);
    InformationObject_destroy(io);
    
    CS104_Slave_enqueueASDU(slave, newAsdu);
    CS101_ASDU_destroy(newAsdu);
}

// Send single point information
void IEC104Server::sendSinglePointInfo(int ioa, bool value, QualityDescriptor quality) {
    if (!slave) return;
    
    CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_SPONTANEOUS, 0, 1, false, false);
    
    InformationObject io = (InformationObject) SinglePointInformation_create(NULL, ioa, value, quality);
    CS101_ASDU_addInformationObject(newAsdu, io);
    InformationObject_destroy(io);
    
    CS104_Slave_enqueueASDU(slave, newAsdu);
    CS101_ASDU_destroy(newAsdu);
}

// Send bit string 32
void IEC104Server::sendBitString32(int ioa, uint32_t value) {
    if (!slave) return;
    
    CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_SPONTANEOUS, 0, 1, false, false);
    
    InformationObject io = (InformationObject) BitString32_create(NULL, ioa, value);
    CS101_ASDU_addInformationObject(newAsdu, io);
    InformationObject_destroy(io);
    
    CS104_Slave_enqueueASDU(slave, newAsdu);
    CS101_ASDU_destroy(newAsdu);
}

// Get application layer parameters
CS101_AppLayerParameters IEC104Server::getAppLayerParameters() {
    return alParams;
}

// Get connection parameters
CS104_APCIParameters IEC104Server::getConnectionParameters() {
    return apciParams;
}

// Get running status
bool IEC104Server::getIsRunning() const {
    return isRunning;
}

// Get connected clients count
int IEC104Server::getConnectedClients() const {
    return connectedClients;
}

// Helper function to print CP56Time2a
void IEC104Server::printCP56Time2a(CP56Time2a time) {
    Serial.printf("%02i:%02i:%02i %02i/%02i/%04i", 
                  CP56Time2a_getHour(time),
                  CP56Time2a_getMinute(time),
                  CP56Time2a_getSecond(time),
                  CP56Time2a_getDayOfMonth(time),
                  CP56Time2a_getMonth(time),
                  CP56Time2a_getYear(time) + 2000);
}

// Print APCI parameters
void IEC104Server::printAPCIParameters() {
    if (apciParams) {
        Serial.println("APCI parameters:");
        Serial.printf("  t0: %i\n", apciParams->t0);
        Serial.printf("  t1: %i\n", apciParams->t1);
        Serial.printf("  t2: %i\n", apciParams->t2);
        Serial.printf("  t3: %i\n", apciParams->t3);
        Serial.printf("  k: %i\n", apciParams->k);
        Serial.printf("  w: %i\n", apciParams->w);
    }
}

// Static callback functions implementation

// Raw message handler callback
void IEC104Server::rawMessageHandler(void* parameter, IMasterConnection connection, uint8_t* msg, int msgSize, bool sent) {
    if (sent)
        Serial.print("SEND: ");
    else
        Serial.print("RCVD: ");

    for (int i = 0; i < msgSize; i++) {
        Serial.printf("%02x ", msg[i]);
    }
    Serial.println();
}

// Clock synchronization handler callback
bool IEC104Server::clockSyncHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu, CP56Time2a newTime) {
    IEC104Server* server = static_cast<IEC104Server*>(parameter);

    Serial.print("Process time sync command with time ");
    server->printCP56Time2a(newTime);
    Serial.println();

    uint64_t newSystemTimeInMs = CP56Time2a_toMsTimestamp(newTime);

    // Set time for ACT_CON message
    CP56Time2a_setFromMsTimestamp(newTime, Hal_getTimeInMs());

    // Update system time here if needed

    return true;
}

// Connection request handler callback
bool IEC104Server::connectionRequestHandler(void* parameter, const char* ipAddress) {
    Serial.printf("New connection request from %s\n", ipAddress);

    // Accept all connections by default
    // You can add IP filtering logic here if needed
    return true;
}

// Connection event handler callback
void IEC104Server::connectionEventHandler(void* parameter, IMasterConnection con, CS104_PeerConnectionEvent event) {
    IEC104Server* server = static_cast<IEC104Server*>(parameter);

    if (event == CS104_CON_EVENT_CONNECTION_OPENED) {
        server->connectedClients++;
        Serial.printf("Connection opened (%p) - Total connections: %d\n", con, server->connectedClients);
    }
    else if (event == CS104_CON_EVENT_CONNECTION_CLOSED) {
        if (server->connectedClients > 0) {
            server->connectedClients--;
        }
        Serial.printf("Connection closed (%p) - Total connections: %d\n", con, server->connectedClients);
    }
    else if (event == CS104_CON_EVENT_ACTIVATED) {
        Serial.printf("Connection activated (%p)\n", con);
    }
    else if (event == CS104_CON_EVENT_DEACTIVATED) {
        Serial.printf("Connection deactivated (%p)\n", con);
    }
}

// Interrogation handler callback
bool IEC104Server::interrogationHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu, uint8_t qoi) {
    IEC104Server* server = static_cast<IEC104Server*>(parameter);

    Serial.printf("Received interrogation for group %i\n", qoi);

    if (qoi == 20) { // Only handle station interrogation
        CS101_AppLayerParameters alParams = IMasterConnection_getApplicationLayerParameters(connection);

        IMasterConnection_sendACT_CON(connection, asdu, false);

        // Send measured values
        CS101_ASDU newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION, 0, 1, false, false);

        InformationObject io = (InformationObject) MeasuredValueScaled_create(NULL, 100, -1, IEC60870_QUALITY_GOOD);
        CS101_ASDU_addInformationObject(newAsdu, io);
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
            MeasuredValueScaled_create((MeasuredValueScaled) io, 101, 23, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
            MeasuredValueScaled_create((MeasuredValueScaled) io, 102, 2300, IEC60870_QUALITY_GOOD));

        InformationObject_destroy(io);
        IMasterConnection_sendASDU(connection, newAsdu);
        CS101_ASDU_destroy(newAsdu);

        // Send single point information
        newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION, 0, 1, false, false);

        io = (InformationObject) SinglePointInformation_create(NULL, 104, true, IEC60870_QUALITY_GOOD);
        CS101_ASDU_addInformationObject(newAsdu, io);
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject)
            SinglePointInformation_create((SinglePointInformation) io, 105, false, IEC60870_QUALITY_GOOD));

        InformationObject_destroy(io);
        IMasterConnection_sendASDU(connection, newAsdu);
        CS101_ASDU_destroy(newAsdu);

        // Send multiple single point information with timestamp
        newAsdu = CS101_ASDU_create(alParams, true, CS101_COT_INTERROGATED_BY_STATION, 0, 1, false, false);

        CS101_ASDU_addInformationObject(newAsdu, io = (InformationObject) SinglePointInformation_create(NULL, 300, true, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 301, false, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 302, true, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 303, false, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 304, true, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 305, false, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 306, true, IEC60870_QUALITY_GOOD));
        CS101_ASDU_addInformationObject(newAsdu, (InformationObject) SinglePointInformation_create((SinglePointInformation) io, 307, false, IEC60870_QUALITY_GOOD));

        InformationObject_destroy(io);
        IMasterConnection_sendASDU(connection, newAsdu);
        CS101_ASDU_destroy(newAsdu);

        // Send bit string
        newAsdu = CS101_ASDU_create(alParams, false, CS101_COT_INTERROGATED_BY_STATION, 0, 1, false, false);

        io = (InformationObject) BitString32_create(NULL, 500, 0xaaaa);
        CS101_ASDU_addInformationObject(newAsdu, io);
        InformationObject_destroy(io);

        IMasterConnection_sendASDU(connection, newAsdu);
        CS101_ASDU_destroy(newAsdu);

        IMasterConnection_sendACT_TERM(connection, asdu);
    }
    else {
        IMasterConnection_sendACT_CON(connection, asdu, true);
    }

    return true;
}

// ASDU handler callback
bool IEC104Server::asduHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu) {
    IEC104Server* server = static_cast<IEC104Server*>(parameter);

    if (CS101_ASDU_getTypeID(asdu) == C_SC_NA_1) {
        Serial.println("Received single command");

        if (CS101_ASDU_getCOT(asdu) == CS101_COT_ACTIVATION) {
            InformationObject io = CS101_ASDU_getElement(asdu, 0);

            if (io) {
                if (InformationObject_getObjectAddress(io) == 5000) {
                    SingleCommand sc = (SingleCommand) io;

                    Serial.printf("IOA: %i switch to %i\n",
                                InformationObject_getObjectAddress(io),
                                SingleCommand_getState(sc));

                    CS101_ASDU_setCOT(asdu, CS101_COT_ACTIVATION_CON);
                }
                else {
                    CS101_ASDU_setCOT(asdu, CS101_COT_UNKNOWN_IOA);
                }

                InformationObject_destroy(io);
            }
            else {
                Serial.println("ERROR: message has no valid information object");
                return true;
            }
        }
        else {
            CS101_ASDU_setCOT(asdu, CS101_COT_UNKNOWN_COT);
        }

        IMasterConnection_sendASDU(connection, asdu);
        return true;
    }

    return false;
}
