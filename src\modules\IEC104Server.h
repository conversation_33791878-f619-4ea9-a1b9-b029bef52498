#ifndef IEC104_SERVER_H
#define IEC104_SERVER_H

#include <Arduino.h>
#include "iec104/lib60870_config.h"
#include "iec104/hal_socket.h"
#include "iec104/iec60870_slave.h"
#include "iec104/cs104_slave.h"
#include "iec104/hal_time.h"
#include "iec104/iec60870_common.h"
#include "iec104/cs104_connection.h"

class IEC104Server {
private:
    CS104_Slave slave;
    CS101_AppLayerParameters alParams;
    CS104_APCIParameters apciParams;
    bool isRunning;
    int connectedClients;
    
    // Static callback functions (required for C library)
    static void rawMessageHandler(void* parameter, IMasterConnection connection, uint8_t* msg, int msgSize, bool sent);
    static bool clockSyncHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu, CP56Time2a newTime);
    static bool interrogationHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu, uint8_t qoi);
    static bool asduHandler(void* parameter, IMasterConnection connection, CS101_ASDU asdu);
    static bool connectionRequestHandler(void* parameter, const char* ipAddress);
    static void connectionEventHandler(void* parameter, IMasterConnection con, CS104_PeerConnectionEvent event);
    
    // Helper functions
    void printCP56Time2a(CP56Time2a time);
    void printAPCIParameters();
    
public:
    // Constructor and Destructor
    IEC104Server();
    ~IEC104Server();
    
    // Configuration methods
    bool initialize(const char* localIP, int maxConnections = 4, int messageQueueSize = 10);
    void setServerMode(CS104_ServerMode mode);
    void enableRawMessageLogging(bool enable);
    
    // Server control methods
    bool start();
    void stop();
    
    // Data sending methods
    void sendMeasuredValue(int ioa, int16_t value, QualityDescriptor quality = IEC60870_QUALITY_GOOD);
    void sendSinglePointInfo(int ioa, bool value, QualityDescriptor quality = IEC60870_QUALITY_GOOD);
    void sendBitString32(int ioa, uint32_t value);
    
    // Status methods
    bool getIsRunning() const;
    int getConnectedClients() const;

    // Utility methods
    CS101_AppLayerParameters getAppLayerParameters();
    CS104_APCIParameters getConnectionParameters();

    
};

#endif // IEC104_SERVER_H
